defmodule Drops.SQL.Introspection do
  @moduledoc """
  Database introspection utilities for extracting schema metadata.

  This module provides functions to introspect database-level information
  that is not available through Ecto schemas, such as index information.

  Uses a behavior-based approach to support multiple database adapters.
  """

  alias Drops.SQL
  alias Drops.SQL.Database.Table

  @doc """
  Introspects a complete table with all metadata.

  This is the main introspection function that returns a complete Table struct
  with columns, primary key, foreign keys, and indices.

  ## Parameters

  - `repo` - The Ecto repository module
  - `table_name` - The name of the table to introspect

  ## Returns

  Returns `{:ok, %Table{}}` on success or `{:error, reason}` on failure.

  ## Examples

      iex> Drops.SQL.Introspection.introspect_table(MyRepo, "users")
      {:ok, %Drops.SQL.Database.Table{name: "users", columns: [...], ...}}
  """
  @spec introspect_table(String.t(), module()) :: {:ok, Table.t()} | {:error, term()}
  def introspect_table(table_name, repo) when is_binary(table_name) do
    case get_database_adapter(repo) do
      {:ok, adapter_module} ->
        adapter_module.table(table_name, repo)

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp get_database_adapter(repo) do
    case repo.__adapter__() do
      Ecto.Adapters.SQLite3 ->
        {:ok, SQL.Sqlite}

      Ecto.Adapters.Postgres ->
        {:ok, SQL.Postgres}

      adapter ->
        {:error, {:unsupported_adapter, adapter}}
    end
  end
end
